import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    Switch,
    TouchableOpacity,
    ActivityIndicator,
    Image,
    Platform,
    Modal,
} from "react-native";
import { FlatList, GestureHandlerRootView } from "react-native-gesture-handler";
import BottomSheet, { BottomSheetBackdrop, BottomSheetScrollView } from "@gorhom/bottom-sheet";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Dimensions } from "react-native";
import { Order } from "@components/orders/types";
import Svg, { Path } from "react-native-svg";
import OrderCard from "@components/cards/orders/OrderCard";
import Toast from "react-native-toast-message";
import api from "@api/api";
import { Status } from "../../types/Order";
import TextButton from "@components/inputs/buttons/TextButton/TextButton";
import ModalBase from "@components/Navigation/ModalView/ModalBase";
import { useOrderStore } from "../../store/orders";
import { Easing } from "react-native-reanimated";

const WarningIcon = ({ size, color }) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        <Path
            d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1-7v2h2v-2h-2zm0-8v6h2V7h-2z"
            fill={color}
        />
    </Svg>
);

const DeleteIcon = ({ size, color }) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        <Path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" fill={color} />
    </Svg>
);

interface BlockCustomerBottomSheetProps {
    visible: boolean;
    orderId: string;
    onClose: () => void;
    onSuccess: (isDeleteAll: boolean) => void;
}

const isSuspicious = (orders: Order[]) => {
    if (orders.length < 3) return false;

    // Check for multiple orders in a short time span (within 24 hours)
    const recentOrders = orders.filter((order) => {
        const orderDate = new Date(order.createdAt);
        const now = new Date();
        return now.getTime() - orderDate.getTime() <= 24 * 60 * 60 * 1000;
    });

    return recentOrders.length >= 3;
};

const EmptyOrdersList = () => (
    <View style={styles.emptyContainer}>
        <Text style={styles.emptyTitle}>No Orders Found</Text>
        <Text style={styles.emptyText}>There are no orders associated with this customer.</Text>
    </View>
);

export const BlockCustomerBottomSheet: React.FC<BlockCustomerBottomSheetProps> = ({
    visible,
    orderId,
    onClose,
    onSuccess,
}) => {
    const {
        customerOrders,
        fetchCustomerOrdersByOrderId,
        blockCustomer,
        deleteCustomerOrders,
        deleteOrder,
        restoreOrder,
    } = useOrderStore();

    // Separate swipeable close state (same pattern as main orders list)
    const [swipeableClose, setSwipeableClose] = useState<(() => void) | undefined>(undefined);

    // Direct pass-through function to match OrderCard's single callback pattern
    const handleSetSwipeableClose = useCallback((callback: () => void) => {
        setSwipeableClose(() => callback);
    }, []);

    // Consolidated state management
    const [state, setState] = useState({
        loading: false,
        shouldDeleteOrders: false,
        selectedOrdersId: [] as string[],
        deleteId: undefined as string | undefined,
        modalVisible: false,
        modalMode: "delete" as "edit" | "delete" | "send" | "restore",
        dataReady: false, // Track when data is fully loaded and ready to display
    });

    // Bottom sheet specific code
    const bottomSheetRef = useRef<BottomSheet>(null);
    const snapPoints = useMemo(() => {
        if (state.loading || !state.dataReady) return ["50%"]; // Default height while loading or data not ready
        if (customerOrders.length === 0) return ["50%"]; // Minimal height for empty state
        if (customerOrders.length <= 1) return Platform.OS === "ios" ? ["45%"] : ["48%"]; // Medium height for 1 order
        if (customerOrders.length <= 2) return Platform.OS === "ios" ? ["60%"] : ["66%"]; // Medium height for 2 orders
        return ["90%"]; // Full height for 3+ orders
    }, [customerOrders.length, state.loading, state.dataReady]);

    const handleSheetChanges = useCallback(
        (index: number) => {
            console.log("index => ", index);
            if (index === -1) {
                // Only reset UI-related state values when sheet is dismissed
                // but don't reset the customer orders data
                setState((prev) => ({
                    ...prev,
                    shouldDeleteOrders: false,
                    selectedOrdersId: [],
                    deleteId: undefined,
                    modalVisible: false,
                    modalMode: "delete",
                    dataReady: false,
                    // Don't reset customerOrders here
                }));
                setSwipeableClose(undefined);
                onClose(); // This calls the parent's onClose function
            }
        },
        [onClose]
    );

    const renderBackdrop = useCallback(
        (props: any) => <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} opacity={0.5} />,
        []
    );

    const fetchCustomerOrders = async () => {
        if (!orderId) return;

        try {
            setState((prev) => ({ ...prev, loading: true, dataReady: false }));
            const orders = await fetchCustomerOrdersByOrderId(orderId);
            console.log(
                "Orders status => ",
                orders.map((order) => order.status)
            );
            // No need to update local state - fetchCustomerOrdersByOrderId updates the global store

            // On Android, add a small delay to ensure proper layout calculation before showing data
            if (Platform.OS === "android") {
                setTimeout(() => {
                    setState((prev) => ({ ...prev, loading: false, dataReady: true }));
                }, 150); // Slightly longer delay to ensure proper layout
            } else {
                setState((prev) => ({ ...prev, loading: false, dataReady: true }));
            }

            return orders;
        } catch (error) {
            console.error("Error fetching customer orders:", error);
            setState((prev) => ({ ...prev, loading: false, dataReady: true }));
            return [];
        }
    };

    useEffect(() => {
        if (visible) {
            bottomSheetRef.current?.expand();
            // Always fetch fresh data when opening to ensure we have the latest state
            fetchCustomerOrders();
            console.log("visible => ", visible);
        } else {
            bottomSheetRef.current?.close();
            // Only reset UI-related state when closing
            setState((prev) => ({
                ...prev,
                shouldDeleteOrders: false,
                selectedOrdersId: [],
                deleteId: undefined,
                modalVisible: false,
                modalMode: "delete",
                dataReady: false,
                // Don't reset customerOrders here
            }));
            setSwipeableClose(undefined);
        }
    }, [visible, orderId]);

    const handleBlockCustomer = async () => {
        try {
            setState((prev) => ({ ...prev, loading: true }));
            await blockCustomer(orderId);

            if (state.shouldDeleteOrders) {
                await deleteCustomerOrders(orderId);
            }

            onSuccess(state.shouldDeleteOrders);
            onClose();
        } catch (error) {
            // Errors are handled in the store
        } finally {
            setState((prev) => ({ ...prev, loading: false }));
        }
    };

    const showWarning = useMemo(() => isSuspicious(customerOrders), [customerOrders]);

    // Android FlatList optimization - provide explicit item layout
    const getItemLayout = useCallback((_data: any, index: number) => {
        // Approximate height of OrderCard based on its content
        // This includes padding, margins, and content height
        const ITEM_HEIGHT = 120; // Adjust this based on your OrderCard height
        const ITEM_SEPARATOR_HEIGHT = 12; // Gap between items from contentContainerStyle

        return {
            length: ITEM_HEIGHT,
            offset: (ITEM_HEIGHT + ITEM_SEPARATOR_HEIGHT) * index,
            index,
        };
    }, []);

    const handleDeleteOrder = async (deleteId: string) => {
        if (!deleteId) return;

        setState((prev) => ({ ...prev, loading: true }));

        try {
            // Use the store function to delete the order (updates both lists)
            const response = await deleteOrder(deleteId);
            console.log("Delete response => ", response);

            if (response) {
                // Check if this is the currently opened order
                const isCurrentOrder = deleteId === orderId; // This is the order ID from props

                // If we're deleting the current order, call onSuccess to update the order details view
                if (isCurrentOrder) {
                    onSuccess(false);
                }
            }
        } catch (error: any) {
            Toast.show({
                type: "error",
                text1: error?.response?.data?.message || "Failed to delete order",
            });
        } finally {
            // Make sure to close the swipeable
            if (swipeableClose) {
                swipeableClose();
            }
            setSwipeableClose(undefined);
            setState((prev) => ({
                ...prev,
                loading: false,
                deleteId: undefined,
            }));
        }
    };

    const handleRestoreOrder = async (restoreId: string) => {
        if (!restoreId) return;

        setState((prev) => ({ ...prev, loading: true }));

        try {
            // Use the store function to restore the order (updates both lists)
            const response = await restoreOrder(restoreId);

            if (response) {
                // Check if this is the currently opened order
                const isCurrentOrder = restoreId === orderId;

                // If we're restoring the current order, call onSuccess to update the order details view
                if (isCurrentOrder) {
                    onSuccess(false);
                }

                // Toast.show({
                //     type: "success",
                //     text1: "Order Restored",
                //     text2: "Order has been successfully restored",
                // });
            }
        } catch (error: any) {
            Toast.show({
                type: "error",
                text1: error?.response?.data?.message || "Failed to restore order",
            });
        } finally {
            // Make sure to close the swipeable
            if (swipeableClose) {
                swipeableClose();
            }
            setSwipeableClose(undefined);
            setState((prev) => ({
                ...prev,
                loading: false,
            }));
        }
    };

    // useEffect(() => {
    //     if (deleteId && modalVisible && modalMode === 'delete') {
    //         handleDeleteOrder(deleteId);
    //         setModalVisible(false);
    //     }
    // }, [deleteId, modalVisible, modalMode]);

    // Add cleanup effect when component unmounts
    useEffect(() => {
        return () => {
            setState((prev) => ({
                ...prev,
                shouldDeleteOrders: false,
                selectedOrdersId: [],
                deleteId: undefined,
                modalVisible: false,
                modalMode: "delete",
                dataReady: false,
            }));
            setSwipeableClose(undefined);
        };
    }, []);

    return (
        <>
            <BottomSheet
                ref={bottomSheetRef}
                index={visible ? 0 : -1}
                snapPoints={snapPoints}
                onChange={handleSheetChanges}
                enablePanDownToClose
                backdropComponent={renderBackdrop}
                backgroundStyle={styles.sheetBackground}
                handleIndicatorStyle={styles.handleIndicator}
                animateOnMount={true}
                activeOffsetY={[-10, 10]}
                animationConfigs={{
                    duration: 300,
                    easing: Easing.inOut(Easing.ease),
                }}
            >
                <View style={styles.container}>
                    {/* Fixed Header Content */}
                    <View style={styles.header}>
                        {/* Warning Section - Only show if suspicious activity detected */}
                        {showWarning && (
                            <View
                                style={[styles.warningSection, { borderWidth: 1, borderColor: colors.secondary[600] }]}
                            >
                                <View style={styles.warningHeader}>
                                    <WarningIcon size={24} color={colors.secondary[600]} />
                                    <Text style={styles.warningTitle}>Suspicious Activity Detected</Text>
                                </View>
                                <Text style={styles.warningText}>
                                    This customer has placed multiple orders in a short time span.
                                    {state.shouldDeleteOrders
                                        ? "\nWarning: All orders from this customer will be deleted."
                                        : ""}
                                </Text>
                            </View>
                        )}

                        {/* Total Orders */}
                        <Text style={styles.totalOrders}>
                            Total Orders :{" "}
                            {state.loading ? (
                                <ActivityIndicator size="small" color={colors.primary[500]} />
                            ) : (
                                customerOrders.length
                            )}
                        </Text>
                    </View>

                    {/* Scrollable Orders List */}
                    <View style={[styles.ordersSection, { width: "100%" }]}>
                        {state.loading || !state.dataReady ? (
                            <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                                <ActivityIndicator size="large" color={colors.primary[500]} />
                            </View>
                        ) : customerOrders.length === 0 ? (
                            <EmptyOrdersList />
                        ) : (
                            <FlatList
                                data={customerOrders && Array.isArray(customerOrders) ? [...customerOrders] : []}
                                renderItem={({ item }) => (
                                    <OrderCard
                                        key={item._id}
                                        order={item}
                                        selectionMode={false}
                                        selectedOrdersId={state.selectedOrdersId}
                                        setSelectedOrdersId={(ids) =>
                                            setState((prev) => ({ ...prev, selectedOrdersId: ids }))
                                        }
                                        setDeleteId={(id) =>
                                            setState((prev) => ({
                                                ...prev,
                                                deleteId: id,
                                                modalVisible: true,
                                                modalMode: item.status === "deleted" ? "restore" : "delete",
                                            }))
                                        }
                                        setSwipeableClose={handleSetSwipeableClose}
                                        setModalVisible={(visible) =>
                                            setState((prev) => ({ ...prev, modalVisible: visible }))
                                        }
                                        setModalMode={(mode) => setState((prev) => ({ ...prev, modalMode: mode }))}
                                        disableInteractions={true}
                                        isBlockCustomerView={true}
                                    />
                                )}
                                keyExtractor={(item: Order, index) => `${item._id}-${index}`}
                                contentContainerStyle={[
                                    styles.ordersList,
                                    { width: "100%", paddingBottom: 15, paddingTop: 5 },
                                ]}
                                showsVerticalScrollIndicator={true}
                                scrollEnabled={
                                    customerOrders && Array.isArray(customerOrders) && customerOrders.length >= 3
                                }
                                // Android-specific optimizations to fix initial rendering issues
                                {...(Platform.OS === "android" && {
                                    getItemLayout: getItemLayout,
                                    initialNumToRender: Math.min(customerOrders.length, 5),
                                    maxToRenderPerBatch: 5,
                                    windowSize: 10,
                                    removeClippedSubviews: false,
                                    updateCellsBatchingPeriod: 100,
                                    legacyImplementation: false,
                                })}
                                // Use dataReady to ensure proper rendering
                                extraData={state.dataReady}
                                waitFor={bottomSheetRef}
                                simultaneousHandlers={bottomSheetRef}

                            />
                        )}
                    </View>

                    {/* Fixed Footer Content */}
                    <View style={styles.footer}>
                        {/* Delete Orders Switch */}
                        <View style={styles.switchContainer}>
                            <Switch
                                value={state.shouldDeleteOrders}
                                onValueChange={(value) => setState((prev) => ({ ...prev, shouldDeleteOrders: value }))}
                                trackColor={{ false: colors.gray[400], true: colors.red[200] }}
                                thumbColor={state.shouldDeleteOrders ? colors.red[500] : colors.white}
                                style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}
                            />
                            <Text style={styles.switchText}>Delete all orders from customer</Text>
                        </View>

                        {/* Action Buttons */}
                        <View style={styles.buttonContainer}>
                            <TouchableOpacity style={styles.cancelButton} onPress={onClose} disabled={state.loading}>
                                <Text style={styles.cancelButtonText}>Cancel</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={styles.blockButton}
                                onPress={handleBlockCustomer}
                                disabled={state.loading}
                            >
                                {state.loading ? (
                                    <ActivityIndicator size="small" color={colors.white} />
                                ) : (
                                    <Text style={styles.blockButtonText}>Block Customer</Text>
                                )}
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </BottomSheet>
            <ModalBase
                visible={state.modalVisible}
                setVisible={(visible: boolean) => {
                    setState((prev) => ({ ...prev, modalVisible: visible }));
                }}
                onDismiss={() => {
                    if (swipeableClose) {
                        swipeableClose(); // Close the swipeable card only on dismiss
                    }
                    setSwipeableClose(undefined);
                    setState((prev) => ({
                        ...prev,
                        modalVisible: false,
                    }));
                }}
            >
                <View style={{ gap: 10 }}>
                    {state.modalMode === "delete" && (
                        <>
                            <Text
                                style={[
                                    typography.fontMedium,
                                    typography.lg,
                                    { color: colors.gray[800], textAlign: "center" },
                                ]}
                            >
                                Delete Order{" "}
                                {state.deleteId && (
                                    <Text style={[typography.altTextMedium, { color: colors.primary[700] }]}>
                                        #{customerOrders.find((o) => o._id === state.deleteId)?.reference}
                                    </Text>
                                )}
                            </Text>
                            <Text style={[typography.fontNormal, typography.md, { color: colors.gray[800] }]}>
                                Are you sure you want to delete Order{" "}
                                <Text style={[typography.altTextMedium, { color: colors.primary[700] }]}>
                                    #{customerOrders.find((o) => o._id === state.deleteId)?.reference}
                                </Text>
                                ?
                            </Text>
                            <View style={{ flexDirection: "row", gap: 10 }}>
                                <TextButton
                                    style={{ flex: 1 }}
                                    label="Cancel"
                                    variant="outlined"
                                    color={colors.red[500]}
                                    onPress={() => {
                                        if (swipeableClose) {
                                            swipeableClose(); // Close the swipeable card on cancel
                                        }
                                        setSwipeableClose(undefined);
                                        setState((prev) => ({
                                            ...prev,
                                            modalVisible: false,
                                        }));
                                    }}
                                />
                                <TextButton
                                    style={{ flex: 1 }}
                                    label="Delete"
                                    variant="contained"
                                    color={colors.red[500]}
                                    onPress={() => {
                                        handleDeleteOrder(state.deleteId);
                                        setState((prev) => ({
                                            ...prev,
                                            modalVisible: false,
                                        }));
                                    }}
                                />
                            </View>
                        </>
                    )}

                    {state.modalMode === "restore" && (
                        <>
                            <Text
                                style={[
                                    typography.fontMedium,
                                    typography.lg,
                                    { color: colors.gray[800], textAlign: "center" },
                                ]}
                            >
                                Restore Order{" "}
                                {state.deleteId && (
                                    <Text style={[typography.altTextMedium, { color: colors.primary[700] }]}>
                                        #{customerOrders.find((o) => o._id === state.deleteId)?.reference}
                                    </Text>
                                )}
                            </Text>
                            <Text style={[typography.fontNormal, typography.md, { color: colors.gray[800] }]}>
                                Are you sure you want to restore Order{" "}
                                <Text style={[typography.altTextMedium, { color: colors.primary[700] }]}>
                                    #{customerOrders.find((o) => o._id === state.deleteId)?.reference}
                                </Text>
                                ?
                            </Text>
                            <View style={{ flexDirection: "row", gap: 10 }}>
                                <TextButton
                                    style={{ flex: 1 }}
                                    label="Cancel"
                                    variant="outlined"
                                    color={colors.green[500]}
                                    onPress={() => {
                                        if (swipeableClose) {
                                            swipeableClose(); // Close the swipeable card
                                        }
                                        setSwipeableClose(undefined);
                                        setState((prev) => ({
                                            ...prev,
                                            modalVisible: false,
                                        }));
                                    }}
                                />
                                <TextButton
                                    style={{ flex: 1 }}
                                    label="Restore"
                                    variant="contained"
                                    color={colors.green[500]}
                                    onPress={() => {
                                        handleRestoreOrder(state.deleteId);
                                        setState((prev) => ({
                                            ...prev,
                                            modalVisible: false,
                                        }));
                                    }}
                                />
                            </View>
                        </>
                    )}
                </View>
            </ModalBase>
        </>
    );
};

const styles = StyleSheet.create({
    sheetBackground: {
        backgroundColor: colors.white,
    },
    handleIndicator: {
        backgroundColor: colors.gray[400],
        width: 40,
    },
    container: {
        flex: 1,
        paddingHorizontal: 20,
    },
    header: {
        gap: 16,
        paddingTop: 12,
    },
    title: {
        ...typography.lg,
        fontFamily: typography.fontSemibold.fontFamily,
        color: colors.gray[900],
        textAlign: "center",
        marginBottom: 8,
    },
    warningSection: {
        backgroundColor: colors.secondary[50],
        padding: 16,
        borderRadius: 12,
        gap: 8,
    },
    warningHeader: {
        flexDirection: "row",
        alignItems: "center",
        gap: 8,
    },
    warningTitle: {
        ...typography.lg,
        fontFamily: typography.fontSemibold.fontFamily,
        color: colors.secondary[600],
    },
    warningText: {
        ...typography.sm,
        color: colors.secondary[600],
    },
    totalOrders: {
        ...typography.lg,
        fontFamily: typography.fontSemibold.fontFamily,
        color: colors.gray[900],
    },
    ordersSection: {
        flex: 1,
        marginVertical: 16, // Adjust this value to control the visible area of the list
    },
    ordersList: {
        gap: 12,
        paddingBottom: 16,
    },
    footer: {
        gap: 16,
        paddingBottom: 30,
        backgroundColor: colors.white,
    },
    switchContainer: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: colors.gray[100],
        padding: 12,
        borderRadius: 12,
        gap: 12,
    },
    switchText: {
        ...typography.sm,
        color: colors.gray[700],
        flex: 1,
    },
    buttonContainer: {
        flexDirection: "row",
        gap: 12,
    },
    cancelButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: "center",
        justifyContent: "center",
        borderWidth: 1,
        borderColor: colors.gray[300],
    },
    blockButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: colors.red[500],
    },
    cancelButtonText: {
        ...typography.sm,
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.gray[700],
    },
    blockButtonText: {
        ...typography.sm,
        fontFamily: typography.fontMedium.fontFamily,
        color: colors.white,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        paddingHorizontal: 20,
        gap: 8,
    },
    emptyTitle: {
        ...typography.lg,
        fontFamily: typography.fontSemibold.fontFamily,
        color: colors.gray[900],
    },
    emptyText: {
        ...typography.sm,
        color: colors.gray[600],
        textAlign: "center",
    },
});
