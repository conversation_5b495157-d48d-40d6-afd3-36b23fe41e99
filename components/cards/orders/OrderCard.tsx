import { StyleSheet, Text, View } from "react-native";
import React, { useRef, memo, useCallback } from "react";
import colors from "@styles/colors";
import { typography } from "@styles/typography";
import { Tag } from "@components/inputs/chips/Tag/Tag";
import { router } from "expo-router";
import { Order } from "@components/orders/types";
import { useOrderStore } from "../../../store/orders";
import { useStoreStore } from "../../../store/storeStore";

import * as Haptics from "expo-haptics";

import Swipeable from "react-native-gesture-handler/ReanimatedSwipeable";
import { EditIcon } from "@components/icons/BudgetManagerIcons";
import { TrashIcon, CheckIcon, RestoreIcon } from "@components/icons/AccountListIcons";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
    interpolateColor,
    runOnJS,
    useAnimatedStyle,
    useSharedValue,
    withTiming,
} from "react-native-reanimated";

export const formatDate = (date: string) => {
    const createdAtDate = new Date(date);
    const formattedDate = `${createdAtDate.getDate().toString().padStart(2, "0")}/${(createdAtDate.getMonth() + 1)
        .toString()
        .padStart(2, "0")}/${createdAtDate.getFullYear()} ${createdAtDate
        .getHours()
        .toString()
        .padStart(2, "0")}:${createdAtDate.getMinutes().toString().padStart(2, "0")}`;
    return formattedDate;
};

export const getInt = (number: number) => {
    return Math.trunc(number);
};

export const getDecimal = (number: number) => {
    return number - getInt(number);
};

export const getDecimalString = (number: number, length: number) => {
    const decimal = getDecimal(number);
    let res = decimal.toFixed(length);

    return res.replace(/^0\./, "");
};

interface OrderCardProps {
    order: Order;
    selectionMode: boolean;
    setDeleteId: (id: string) => void;
    selectedOrdersId: string[];
    setSelectedOrdersId: (ids: string[]) => void;
    setSwipeableClose: (callback: () => () => void) => void;
    setModalVisible: (visible: boolean) => void;
    setModalMode: (mode: "edit" | "delete" | "send" | "restore") => void;
    disableInteractions?: boolean;
    isBlockCustomerView?: boolean;
}

const OrderCard = memo(
    ({
        order,
        selectionMode,
        setDeleteId,
        selectedOrdersId,
        setSelectedOrdersId,
        setSwipeableClose,
        setModalVisible,
        setModalMode,
        disableInteractions = false,
    }: OrderCardProps) => {
        const swipeableRef = useRef<any>(null);
        const { setOrder } = useOrderStore();
        const { store } = useStoreStore();

        const selected = selectedOrdersId.includes(order._id);

        const handleCardClick = useCallback(() => {
            if (disableInteractions) return;
            setOrder(order);
            router.navigate({
                pathname: "/order/orderDetails",
                params: { order: order._id },
            });
        }, [disableInteractions, order, setOrder]);

        const toggleSelection = useCallback(() => {
            if (disableInteractions) return;
            Haptics.impactAsync();
            setSelectedOrdersId(
                selectedOrdersId.includes(order._id)
                    ? selectedOrdersId.filter((id: string) => id !== order._id)
                    : [...selectedOrdersId, order._id]
            );
        }, [disableInteractions, selectedOrdersId, order._id, setSelectedOrdersId]);

        const handleLongPress = useCallback(() => {
            if (disableInteractions) return;
            toggleSelection();
        }, [disableInteractions, toggleSelection]);

        const handleTap = useCallback(() => {
            if (disableInteractions) return;
            if (!selectionMode) {
                handleCardClick();
            } else {
                toggleSelection();
            }
        }, [disableInteractions, selectionMode, handleCardClick, toggleSelection]);

        const longPress = Gesture.LongPress().onStart(() => {
            runOnJS(handleLongPress)();
        });

        const tap = Gesture.Tap().onStart(() => {
            runOnJS(handleTap)();
        });

        const emptyGesture = Gesture.Tap().enabled(false);
        const gestures = disableInteractions ? emptyGesture : Gesture.Race(tap, longPress);

        const selectedAnimation = useAnimatedStyle(() => {
            const color = interpolateColor(selected ? 1 : 0, [0, 1], [colors.blue[50], colors.blue[100]]);
            const colorValue = withTiming(color, { duration: 100 });

            return {
                backgroundColor: colorValue,
            };
        });

        return (
            <Swipeable
                ref={swipeableRef}
                enabled={true}
                overshootLeft={false}
                onSwipeableWillOpen={(direction) => {
                    if (direction === "right") {
                        if (order.status === "deleted") {
                            // Show restore modal for deleted orders
                            setDeleteId(order._id);
                            setModalVisible(true);
                            setModalMode("restore");
                            setSwipeableClose(() =>  swipeableRef?.current?.close());
                        } else {
                            setDeleteId(order._id);
                            setModalVisible(true);
                            setModalMode("delete");
                            setSwipeableClose(() =>  swipeableRef?.current?.close());
                        }
                    } else if (!disableInteractions) {
                        setOrder(order);
                        router.navigate({ params: { id: order._id }, pathname: "/order/edit" });
                        swipeableRef?.current?.close();
                    }
                }}
                friction={2}
                renderLeftActions={() => {
                    if (disableInteractions || selectionMode) return null;
                    return (
                        <View
                            style={{
                                width: "40%",
                                marginLeft: 10,
                                marginRight: -30,
                                borderRadius: 10,
                                overflow: "hidden",
                            }}
                        >
                            <View
                                style={{
                                    width: "100%",
                                    height: "100%",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    gap: 2,
                                    backgroundColor: colors.orange[400],
                                    paddingRight: 15,
                                }}
                            >
                                <EditIcon color={colors.white} size={32} />
                                <Text style={styles.actionlabel}>Edit</Text>
                            </View>
                        </View>
                    );
                }}
                renderRightActions={() => {
                    if (selectionMode) return null;
                    return (
                        <View
                            style={{
                                width: "40%",
                                marginRight: 10,
                                marginLeft: -30,
                                borderRadius: 10,
                                overflow: "hidden",
                            }}
                        >
                            <View
                                style={{
                                    height: "100%",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    gap: 2,
                                    backgroundColor: order.status === "deleted" ? colors.green[500] : colors.red[500],
                                    paddingLeft: 15,
                                }}
                            >
                                {order.status === "deleted" ? (
                                    <>
                                        <RestoreIcon color={colors.white} size={22} />
                                        <Text style={[styles.actionlabel, { textAlign: "center" }]}>Restore</Text>
                                    </>
                                ) : (
                                    <>
                                        <TrashIcon color={colors.white} size={32} />
                                        <Text style={styles.actionlabel}>Delete</Text>
                                    </>
                                )}
                            </View>
                        </View>
                    );
                }}
            >
                <GestureDetector gesture={gestures}>
                    <Animated.View
                        style={[
                            {
                                marginHorizontal: 10,
                                paddingHorizontal: 15,
                                paddingVertical: 10,
                                borderRadius: 10,
                                gap: 25,
                                borderWidth: 1,
                                borderColor: colors.gray[300],
                                backgroundColor: colors.blue[50],
                            },
                            selectedAnimation,
                        ]}
                    >
                        <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center" }}>
                            <View>
                                <View style={{ flexDirection: "row", alignItems: "center", gap: 5 }}>
                                    <Text
                                        style={{
                                            fontFamily: typography.fontBold.fontFamily,
                                            color: colors.primary[500],
                                            fontSize: 16,
                                            gap: 5,
                                        }}
                                    >
                                        #{order?.reference}
                                    </Text>
                                    {selected && (
                                        <>
                                            <CheckIcon color={colors.primary[500]} size={20} />
                                        </>
                                    )}
                                </View>
                                <Text
                                    style={{
                                        color: colors.gray[500],
                                        fontFamily: typography.fontMedium.fontFamily,
                                        fontSize: 12,
                                        marginRight: 10,
                                    }}
                                >
                                    {formatDate(String(order.createdAt))}
                                </Text>
                            </View>
                            {order.isTest && (order?.status === "pending" || order?.status === "abandoned") ? (
                                <Tag status={"test"} />
                            ) : order.duplicated && (order?.status === "pending" || order?.status === "abandoned") ? (
                                <Tag status={"duplicated"} />
                            ) : (
                                <Tag status={order?.status} attempt={order.attempt} />
                            )}
                        </View>
                        <View
                            style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                alignItems: "flex-end",
                            }}
                        >
                            <View style={{ flex: 2 }}>
                                <Text
                                    style={{
                                        fontSize: 18,
                                        textTransform: "capitalize",
                                        fontFamily: typography.fontSemibold.fontFamily,
                                        color: colors.gray[600],
                                    }}
                                >
                                    {order?.customer.name}
                                </Text>
                                <Text
                                    style={{
                                        fontFamily: typography.fontSemibold.fontFamily,
                                        color: colors.gray[500],
                                        fontSize: 14,
                                        marginRight: 10,
                                    }}
                                >
                                    {order?.customer.phone}
                                </Text>
                            </View>
                            <Text
                                style={{
                                    textAlign: "right",
                                    color: colors.primary[600],
                                    fontFamily: typography.fontBold.fontFamily,
                                    fontSize: 24,
                                    flex: 1,
                                }}
                            >
                                <Text>{getInt(order?.total.totalPrice)}</Text>
                                <Text style={{ fontSize: 16 }}>.{getDecimalString(order?.total.totalPrice, 2)}</Text>
                                <Text
                                    style={{
                                        fontFamily: typography.fontMedium.fontFamily,
                                        fontSize: 14,
                                        marginLeft: 2,
                                    }}
                                >
                                    {store.currency?.code}
                                </Text>
                            </Text>
                        </View>
                    </Animated.View>
                </GestureDetector>
            </Swipeable>
        );
    },
    (prevProps, nextProps) => {
        // Custom comparison function for React.memo
        return (
            prevProps.order._id === nextProps.order._id &&
            prevProps.order.reference === nextProps.order.reference &&
            prevProps.order.status === nextProps.order.status &&
            prevProps.order.total?.totalPrice === nextProps.order.total?.totalPrice &&
            prevProps.selectionMode === nextProps.selectionMode &&
            prevProps.selectedOrdersId.length === nextProps.selectedOrdersId.length &&
            prevProps.selectedOrdersId.includes(prevProps.order._id) ===
                nextProps.selectedOrdersId.includes(nextProps.order._id) &&
            prevProps.disableInteractions === nextProps.disableInteractions &&
            prevProps.isBlockCustomerView === nextProps.isBlockCustomerView
        );
    }
);

export default OrderCard;

const styles = StyleSheet.create({
    action: {
        borderRadius: 10,
        width: "80%",
        justifyContent: "center",
    },
    actionlabel: {
        fontFamily: typography.altTextMedium.fontFamily,
        fontSize: typography.md.fontSize,
        color: colors.white,
    },
});
