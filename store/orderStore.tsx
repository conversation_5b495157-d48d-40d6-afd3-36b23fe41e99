/**
 * Legacy Order Store
 *
 * This file maintains backward compatibility while delegating to the new
 * refactored order stores. All new code should use the stores in /store/orders/
 *
 * @deprecated Use individual stores from /store/orders/ for new code
 */

import { useOrderStore as useNewOrderStore } from "./orders";

// Re-export the unified store for backward compatibility
// export const useOrderStore = useNewOrderStore;
