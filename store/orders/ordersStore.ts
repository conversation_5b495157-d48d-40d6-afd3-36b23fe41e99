import { create } from "zustand";
import { produce } from "immer";
import { Order, ProductsList } from "@components/orders/types";
import { OrderService } from "../../services/orderService";
import { ToastHandler } from "../../utils/ToastHandler";
import { OrderUtils } from "../../utils/orderUtils";
import { useLoaderStore } from "../loaderStore";

// Types
interface OrdersState {
    orders: Order[];
    loading: boolean;
    error?: string | null;
}

interface OrdersActions {
    // Basic state management
    setOrders: (orders: Order[]) => void;
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    
    // Order list operations
    addOrderToList: (order: Order) => void;
    updateOrderInList: (reference: number, updatedOrder: Partial<Order>) => void;
    removeOrderFromList: (orderId: string) => void;
    pushToOrders: (newOrders: Order[]) => void;
    updateMultipleOrders: (updatedOrders: Order[]) => void;
    
    // Socket event handlers
    handleOrderRestored: (restoredOrder: Order) => void;
}

type OrdersStore = OrdersState & OrdersActions;

export const useOrdersStore = create<OrdersStore>((set, get) => ({
    // Initial state
    orders: [],
    loading: false,
    error: null,

    // Basic state management
    setOrders: (orders) => {
        set({ orders });
    },

    setLoading: (loading) => {
        set({ loading });
    },

    setError: (error) => {
        set({ error });
    },

    // Add order to list with duplicate checking
    addOrderToList: (order) => {
        const { orders } = get();
        const { orders: updatedOrders, wasAdded } = OrderUtils.addOrderIfNotExists(orders, order);
        
        if (wasAdded) {
            set({ orders: updatedOrders });
            ToastHandler.showOrderSuccess("Created", order.reference);
            console.log(`Order ${order.reference} added to list. New count: ${updatedOrders.length}`);
        }
    },

    // Update order in list by reference
    updateOrderInList: (reference, updatedOrder) => {
        const { orders } = get();
        const { orders: newOrders, wasUpdated } = OrderUtils.updateOrderByReference(
            orders, 
            reference, 
            updatedOrder
        );

        if (wasUpdated) {
            set({ orders: newOrders });
        } else {
            ToastHandler.showErrorToast(
                "Update Failed",
                "Order not found in the list"
            );
        }
    },

    // Remove order from list
    removeOrderFromList: (orderId) => {
        const { orders } = get();
        
        if (!orderId) {
            console.log("Invalid order ID provided for deletion");
            return;
        }

        const { orders: newOrders, wasRemoved, removedOrder } = OrderUtils.removeOrderById(orders, orderId);

        if (wasRemoved && removedOrder) {
            set({ orders: newOrders });
            
            // Show toast notification
            ToastHandler.showOrderInfo(
                "Deleted",
                removedOrder.reference,
                `Order #${removedOrder.reference} has been removed`
            );
        } else {
            // Order might already be deleted, show generic message
            ToastHandler.showOrderInfo("Deleted", undefined, "Order has been deleted");
        }
    },

    // Add multiple orders to list
    pushToOrders: (newOrders) => {
        set(({ orders }) => ({
            orders: [...orders, ...newOrders]
        }));
    },

    // Update multiple orders
    updateMultipleOrders: (updatedOrders) => {
        const { orders } = get();
        const mergedOrders = OrderUtils.mergeOrderUpdates(orders, updatedOrders);
        set({ orders: mergedOrders });
    },

    // Handle order restoration from socket
    handleOrderRestored: (restoredOrder) => {
        const { orders } = get();

        // Validate the restored order
        const { isValid, errors } = OrderUtils.validateOrder(restoredOrder);
        if (!isValid) {
            console.log(`Invalid order data received for restoration: ${errors.join(', ')}`);
            ToastHandler.showErrorToast(
                "Restoration Failed",
                "Invalid order data received"
            );
            return;
        }

        console.log(`Restoring order ${restoredOrder.reference} from socket`);

        set(
            produce((state) => {
                // Check if the order already exists in the list
                const existingOrderIndex = state.orders.findIndex((o) => o._id === restoredOrder._id);

                if (existingOrderIndex !== -1) {
                    // Update existing order
                    state.orders[existingOrderIndex] = restoredOrder;
                    console.log(`Updated existing order ${restoredOrder.reference} in orders list`);
                } else {
                    // Check if order should be added to current list based on reference range
                    if (OrderUtils.isOrderInRange(state.orders, restoredOrder.reference)) {
                        state.orders = OrderUtils.insertOrderAtCorrectPosition(state.orders, restoredOrder);
                        console.log(`Inserted order ${restoredOrder.reference} at correct position`);
                    } else {
                        // If order reference is higher than current highest, add to top
                        if (state.orders.length > 0 && restoredOrder.reference > state.orders[0].reference) {
                            state.orders = [restoredOrder, ...state.orders];
                            console.log(`Added order ${restoredOrder.reference} to top of list`);
                        } else {
                            // Order is lower than current range, just show toast
                            ToastHandler.showOrderInfo("Restored", restoredOrder.reference, "Order restored outside current range");
                        }
                        // console.log(`Order ${restoredOrder.reference} is outside the current range`);
                    }
                }
            })
        );

        // Show appropriate toast message
        if (OrderUtils.isOrderInRange(orders, restoredOrder.reference)) {
            ToastHandler.showOrderSuccess(
                "Restored",
                restoredOrder.reference,
                `Order #${restoredOrder.reference} has been restored`
            );
        } else {
            ToastHandler.showOrderInfo("Restored", undefined, "Order has been restored");
        }
    },
}));
